# 通讯录功能使用说明

## 快速开始

### 1. 功能概述
已成功将腾讯IM TUIKit的好友数据获取方式改为基于接口的通讯录模式，支持：
- 根据用户角色（家长/老师）自动调用对应接口
- 班级切换功能
- 学生列表展示
- 完全兼容原有TUIKit组件

### 2. 核心文件
```
src/TUIKit/services/addressBookService.js          # 通讯录数据服务
src/TUIKit/components/TUIContact/contact-list/index.vue  # 联系人列表组件
src/TUIKit/interface.ts                            # 类型定义扩展
src/pages/test-addressbook/index.vue               # 功能测试页面
```

### 3. 接口配置
```javascript
// 家长接口
/app/addressBookApp/getElternAddressBook

// 老师接口  
/app/addressBookApp/getEmployeeAddressBook
```

### 4. 数据格式
接口返回格式：
```json
{
    "adlist": [
        {
            "id": "784148883587465218",
            "name": "3班",
            "studentList": [
                {
                    "id": "1869559158148198401",
                    "name": "小小静",
                    "className": "3班",
                    "avatar": "",
                    "gender": 0,
                    "address": "地址信息",
                    "phone": ""
                }
            ]
        }
    ],
    "schoolBadgeUrl": "",
    "schoolName": "学校名称"
}
```

## 使用方法

### 1. 自动初始化
组件会在挂载时自动：
- 获取当前用户角色 (`user.identityInfo.roleCode`)
- 调用对应的通讯录接口
- 渲染班级选择器和学生列表

### 2. 班级切换
用户可以点击班级选择器中的班级按钮来切换不同班级，学生列表会自动更新。

### 3. 数据适配
学生数据会自动转换为TUIKit兼容的Friend格式：
```javascript
{
    userID: student.id,
    profile: {
        userID: student.id,
        avatar: student.avatar,
        nick: student.name
    },
    remark: student.name,
    studentInfo: {
        // 保留原始学生信息
        id: student.id,
        name: student.name,
        className: student.className,
        // ...其他字段
    },
    renderKey: `student_${student.id}`,
    isStudent: true
}
```

## 测试验证

### 1. 测试页面
访问 `/pages/test-addressbook/index` 可以测试：
- 通讯录数据获取
- 班级切换功能
- 学生列表展示
- 错误处理

### 2. 控制台日志
开启控制台可以看到详细的执行日志：
```
[AddressBookService] 获取通讯录数据，角色: eltern, 接口: /app/addressBookApp/getElternAddressBook
[ContactList] 通讯录数据初始化完成
[ContactList] 切换班级: 784148883587465218
[ContactList] 更新好友列表，学生数量: 25
```

## 注意事项

### 1. 用户角色
确保用户登录后设置了正确的角色信息：
- `eltern`: 家长
- 其他值: 默认为老师

### 2. 数据兼容性
- 新的学生数据完全兼容TUIKit的Friend接口
- 保留了原有的所有功能和交互
- 通过`isStudent`字段可以区分学生数据和真实好友数据

### 3. 错误处理
- 网络异常会显示错误提示
- 数据为空时会显示空状态
- 加载过程中会显示loading状态

## 扩展开发

### 1. 添加新的用户角色
在 `addressBookService.js` 中的 `getAddressBookByRole` 方法中添加新的角色判断：
```javascript
if (roleCode === 'newRole') {
    url = '/app/addressBookApp/getNewRoleAddressBook'
}
```

### 2. 自定义数据格式
修改 `formatStudentList` 方法来适配不同的数据结构。

### 3. 添加新功能
可以在班级选择器中添加搜索、筛选等功能。

## 常见问题

### Q: 为什么看不到班级选择器？
A: 检查用户是否已登录，角色信息是否正确，接口是否返回了数据。

### Q: 切换班级后学生列表没有更新？
A: 检查控制台是否有错误日志，确认接口返回的数据格式是否正确。

### Q: 如何区分学生数据和真实好友？
A: 通过 `isStudent` 字段可以区分，学生数据该字段为 `true`。

## 技术支持

如有问题，请查看：
1. 控制台错误日志
2. 网络请求是否成功
3. 用户角色信息是否正确
4. 接口返回数据格式是否符合预期
