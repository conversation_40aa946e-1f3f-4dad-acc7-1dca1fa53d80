<template>
  <!-- 班级选择器 -->
  <div v-if="addressBookData.classList.length > 0" class="tui-class-selector">
    <div class="tui-class-selector-header">
      <span class="tui-class-selector-title">选择班级</span>
      <span v-if="addressBookData.schoolInfo.name" class="tui-class-selector-school">
        {{ addressBookData.schoolInfo.name }}
      </span>
    </div>
    <div class="tui-class-selector-options">
      <div
        v-for="classItem in addressBookData.classList"
        :key="classItem.id"
        :class="[
          'tui-class-selector-option',
          currentClassId === classItem.id ? 'active' : ''
        ]"
        @click="switchClass(classItem.id)"
      >
        {{ classItem.name }}
        <span class="tui-class-selector-count">({{ classItem.studentList.length }})</span>
      </div>
    </div>
  </div>

  <!-- 加载状态 -->
  <div v-if="isLoadingAddressBook" class="tui-loading">
    <div class="tui-loading-text">正在加载通讯录...</div>
  </div>

  <ul
    v-if="!contactSearchingStatus && !isLoadingAddressBook"
    :class="['tui-contact-list', !isPC && 'tui-contact-list-h5']"
  >
    <li
      v-for="(contactListObj, key) in filteredContactListMap"
      :key="key"
      class="tui-contact-list-item"
    >
      <template v-if="contactListObj">
        <header
          class="tui-contact-list-item-header"
          @click="toggleCurrentContactList(key)"
        >
          <div class="tui-contact-list-item-header-left">
            <Icon
              :file="currentContactListKey === key ? downSVG : rightSVG"
              width="16px"
              height="16px"
            />
            <div>{{ TUITranslateService.t(`TUIContact.${contactListObj.title}`) }}</div>
          </div>
          <div class="tui-contact-list-item-header-right">
            <span
              v-if="contactListObj.unreadCount"
              class="tui-contact-list-item-header-right-unread"
            >
              {{ contactListObj.unreadCount }}
            </span>
          </div>
        </header>
        <ul :class="['tui-contact-list-item-main', currentContactListKey === key ? '' : 'hidden']">
          <li
            v-for="contactListItem in contactListObj.list"
            :key="contactListItem.renderKey"
            class="tui-contact-list-item-main-item"
            :class="['selected']"
            @click="selectItem(contactListItem)"
          >
            <ContactListItem
              :key="contactListItem.renderKey"
              :item="deepCopy(contactListItem)"
              :displayOnlineStatus="displayOnlineStatus && key === 'friendList'"
            />
          </li>
        </ul>
      </template>
    </li>
  </ul>
  <ul
    v-else
    class="tui-contact-list"
  >
    <li
      v-for="(item, key) in contactSearchResult"
      :key="key"
      class="tui-contact-list-item"
    >
      <div
        v-if="item.list[0]"
        class="tui-contact-search-list"
      >
        <div class="tui-contact-search-list-title">
          {{ TUITranslateService.t(`TUIContact.${item.label}`) }}
        </div>
        <div
          v-for="(listItem, index) in item.list"
          :key="index"
          class="tui-contact-search-list-item"
          :class="['selected']"
          @click="selectItem(listItem)"
        >
          <ContactListItem
            :item="listItem"
            :displayOnlineStatus="false"
          />
        </div>
      </div>
    </li>
    <div
      v-if="isContactSearchNoResult"
      class="tui-contact-search-list-default"
    >
      {{ TUITranslateService.t("TUIContact.无搜索结果") }}
    </div>
  </ul>
</template>
<script setup lang="ts">
import {
  TUITranslateService,
  TUIStore,
  StoreName,
  IGroupModel,
  TUIFriendService,
  Friend,
  FriendApplication,
  TUIUserService,
} from '@tencentcloud/chat-uikit-engine';
import TUICore, { TUIConstants } from '@tencentcloud/tui-core';
import { ref, computed, onMounted, onUnmounted, provide } from '../../../adapter-vue';
import Icon from '../../common/Icon.vue';
import downSVG from '../../../assets/icon/down-icon.svg';
import rightSVG from '../../../assets/icon/right-icon.svg';
import {
  IContactList,
  IContactSearchResult,
  IBlackListUserItem,
  IUserStatus,
  IUserStatusMap,
  IContactInfoType,
} from '../../../interface';
import ContactListItem from './contact-list-item/index.vue';
import { deepCopy } from '../../TUIChat/utils/utils';
import { isPC } from '../../../utils/env';
// 导入通讯录服务和用户store
import addressBookService from '../../../services/addressBookService.js';
import useStore from '@/store';

// 获取用户store
const { user } = useStore();

const currentContactListKey = ref<keyof IContactList>('');
const currentContactInfo = ref<IContactInfoType>({} as IContactInfoType);
const contactListMap = ref<IContactList>({
  friendApplicationList: {
    key: 'friendApplicationList',
    title: '新的联系人',
    list: [] as FriendApplication[],
    unreadCount: 0,
  },
  blackList: {
    key: 'blackList',
    title: '黑名单',
    list: [] as IBlackListUserItem[],
  },
  groupList: {
    key: 'groupList',
    title: '我的群聊',
    list: [] as IGroupModel[],
  },
  friendList: {
    key: 'friendList',
    title: '我的好友',
    list: [] as Friend[],
  },
});
const contactSearchingStatus = ref<boolean>(false);
const contactSearchResult = ref<IContactSearchResult>();
const displayOnlineStatus = ref<boolean>(false);
const userOnlineStatusMap = ref<IUserStatusMap>();

// 通讯录相关状态
const addressBookData = ref<any>({
  classList: [],
  schoolInfo: { badgeUrl: '', name: '' }
});
const currentClassId = ref<string>('');
const isLoadingAddressBook = ref<boolean>(false);

const isContactSearchNoResult = computed((): boolean => {
  return (
    !contactSearchResult?.value?.user?.list[0]
    && !contactSearchResult?.value?.group?.list[0]
  );
});

// 过滤后的联系人列表，只显示好友
const filteredContactListMap = computed(() => {
  const filtered: Partial<IContactList> = {};
  // 只保留好友列表
  if (contactListMap.value.friendList) {
    filtered.friendList = contactListMap.value.friendList;
  }
  return filtered;
});

// 当前班级信息
const currentClassInfo = computed(() => {
  if (!currentClassId.value || !addressBookData.value.classList.length) {
    return addressBookData.value.classList[0] || null;
  }
  return addressBookData.value.classList.find(cls => cls.id === currentClassId.value) || null;
});

// 班级选项列表
const classOptions = computed(() => {
  return addressBookData.value.classList.map(cls => ({
    label: cls.name,
    value: cls.id
  }));
});

onMounted(() => {
  // 初始化通讯录数据
  initAddressBook();

  TUIStore.watch(StoreName.APP, {
    enabledCustomerServicePlugin: onCustomerServiceCommercialPluginUpdated,
  });

  TUIStore.watch(StoreName.GRP, {
    groupList: onGroupListUpdated,
  });

  TUIStore.watch(StoreName.USER, {
    userBlacklist: onUserBlacklistUpdated,
    displayOnlineStatus: onDisplayOnlineStatusUpdated,
    userStatusList: onUserStatusListUpdated,
  });

  // 注释掉原有的好友数据监听，改为使用通讯录数据
  // TUIStore.watch(StoreName.FRIEND, {
  //   friendList: onFriendListUpdated,
  //   friendApplicationList: onFriendApplicationListUpdated,
  //   friendApplicationUnreadCount: onFriendApplicationUnreadCountUpdated,
  // });

  TUIStore.watch(StoreName.CUSTOM, {
    currentContactSearchingStatus: onCurrentContactSearchingStatusUpdated,
    currentContactSearchResult: onCurrentContactSearchResultUpdated,
    currentContactListKey: onCurrentContactListKeyUpdated,
    currentContactInfo: onCurrentContactInfoUpdated,
  });
});

onUnmounted(() => {
  TUIStore.unwatch(StoreName.APP, {
    enabledCustomerServicePlugin: onCustomerServiceCommercialPluginUpdated,
  });

  TUIStore.unwatch(StoreName.GRP, {
    groupList: onGroupListUpdated,
  });

  TUIStore.unwatch(StoreName.USER, {
    userBlacklist: onUserBlacklistUpdated,
    displayOnlineStatus: onDisplayOnlineStatusUpdated,
    userStatusList: onUserStatusListUpdated,
  });

  TUIStore.unwatch(StoreName.FRIEND, {
    friendList: onFriendListUpdated,
    friendApplicationList: onFriendApplicationListUpdated,
    friendApplicationUnreadCount: onFriendApplicationUnreadCountUpdated,
  });

  TUIStore.unwatch(StoreName.CUSTOM, {
    currentContactSearchingStatus: onCurrentContactSearchingStatusUpdated,
    currentContactSearchResult: onCurrentContactSearchResultUpdated,
    currentContactListKey: onCurrentContactListKeyUpdated,
    currentContactInfo: onCurrentContactInfoUpdated,
  });
});

// 初始化通讯录数据
async function initAddressBook() {
  try {
    isLoadingAddressBook.value = true;
    console.log('[ContactList] 开始初始化通讯录数据');

    // 获取用户角色
    const roleCode = user.identityInfo?.roleCode || 'teacher';
    console.log('[ContactList] 用户角色:', roleCode);

    // 获取通讯录数据
    const data = await addressBookService.getAddressBookByRole(roleCode);
    addressBookData.value = data;

    // 设置默认班级（第一个班级）
    if (data.classList.length > 0) {
      currentClassId.value = data.classList[0].id;
      // 更新好友列表为默认班级的学生列表
      updateFriendListWithStudents(data.classList[0].studentList);
    }

    console.log('[ContactList] 通讯录数据初始化完成:', data);
  } catch (error) {
    console.error('[ContactList] 通讯录数据初始化失败:', error);
  } finally {
    isLoadingAddressBook.value = false;
  }
}

// 切换班级
function switchClass(classId: string) {
  console.log('[ContactList] 切换班级:', classId);
  currentClassId.value = classId;

  // 获取对应班级的学生列表
  const students = addressBookService.getStudentsByClassId(addressBookData.value.classList, classId);
  updateFriendListWithStudents(students);
}

// 更新好友列表为学生数据
function updateFriendListWithStudents(students: any[]) {
  console.log('[ContactList] 更新好友列表，学生数量:', students.length);
  contactListMap.value.friendList.list = students;
  contactListMap.value.friendList.title = `我的好友 (${students.length})`;

  // 如果当前展开的是好友列表，保持展开状态
  if (currentContactListKey.value !== 'friendList' && students.length > 0) {
    currentContactListKey.value = 'friendList';
    TUIStore.update(StoreName.CUSTOM, 'currentContactListKey', 'friendList');
  }
}

function toggleCurrentContactList(key: keyof IContactList) {
  if (currentContactListKey.value === key) {
    currentContactListKey.value = '';
    currentContactInfo.value = {} as IContactInfoType;
    TUIStore.update(StoreName.CUSTOM, 'currentContactListKey', '');
    TUIStore.update(StoreName.CUSTOM, 'currentContactInfo', {} as IContactInfoType);
  } else {
    currentContactListKey.value = key;
    TUIStore.update(StoreName.CUSTOM, 'currentContactListKey', key);
    if (key === 'friendApplicationList') {
      TUIFriendService.setFriendApplicationRead();
    }
  }
}

function selectItem(item: any) {
  currentContactInfo.value = item;
  // For a result in the search list, before viewing the contactInfo details,
  // it is necessary to update the data for the "already in the group list/already in the friend list" situation to obtain more detailed information
  if (contactSearchingStatus.value) {
    let targetListItem;
    if ((currentContactInfo.value as Friend)?.userID) {
      targetListItem = contactListMap.value?.friendList?.list?.find(
        (item: IContactInfoType) => (item as Friend)?.userID === (currentContactInfo.value as Friend)?.userID,
      );
    } else if ((currentContactInfo.value as IGroupModel)?.groupID) {
      targetListItem = contactListMap.value?.groupList?.list?.find(
        (item: IContactInfoType) => (item as IGroupModel)?.groupID === (currentContactInfo.value as IGroupModel)?.groupID,
      );
    }
    if (targetListItem) {
      currentContactInfo.value = targetListItem;
    }
  }
  TUIStore.update(StoreName.CUSTOM, 'currentContactInfo', currentContactInfo.value);
}

function onDisplayOnlineStatusUpdated(status: boolean) {
  displayOnlineStatus.value = status;
}

function onUserStatusListUpdated(list: Map<string, IUserStatus>) {
  if (list?.size > 0) {
    userOnlineStatusMap.value = Object.fromEntries(list?.entries());
  }
}

function onCustomerServiceCommercialPluginUpdated(isEnabled: boolean) {
  if (!isEnabled) {
    return;
  }

  // After the customer purchases the customer service plug-in,
  // the engine updates the enabledCustomerServicePlugin to true through the commercial capability bit.
  const contactListExtensionID = TUIConstants.TUIContact.EXTENSION.CONTACT_LIST.EXT_ID;
  const tuiContactExtensionList = TUICore.getExtensionList(contactListExtensionID);

  const customerData = tuiContactExtensionList.find((extension: any) => {
    const { name, accountList = [] } = extension.data || {};
    return name === 'customer' && accountList.length > 0;
  });

  if (customerData) {
    const { data, text } = customerData;
    const { accountList } = (data || {}) as { accountList: string[] };

    TUIUserService.getUserProfile({ userIDList: accountList })
      .then((res) => {
        if (res.data.length > 0) {
          const customerList = {
            title: text,
            list: res.data.map((item: any, index: number) => {
              return {
                ...item,
                renderKey: generateRenderKey('customerList', item, index),
                infoKeyList: [],
                btnKeyList: ['enterC2CConversation'],
              };
            }),
            key: 'customerList',
          };
          contactListMap.value = { ...contactListMap.value, customerList };
        }
      })
      .catch(() => { });
  }
}

function onGroupListUpdated(groupList: IGroupModel[]) {
  updateContactListMap('groupList', groupList);
}

function onUserBlacklistUpdated(userBlacklist: IBlackListUserItem[]) {
  updateContactListMap('blackList', userBlacklist);
}

function onFriendApplicationUnreadCountUpdated(friendApplicationUnreadCount: number) {
  contactListMap.value.friendApplicationList.unreadCount = friendApplicationUnreadCount;
}

function onFriendListUpdated(friendList: Friend[]) {
  updateContactListMap('friendList', friendList);
}

function onFriendApplicationListUpdated(friendApplicationList: FriendApplication[]) {
  updateContactListMap('friendApplicationList', friendApplicationList);
}

function updateContactListMap(key: keyof IContactList, list: IContactInfoType[]) {
  contactListMap.value[key].list = list;
  contactListMap.value[key].list.map((item: IContactInfoType, index: number) => item.renderKey = generateRenderKey(key, item, index));
  updateCurrentContactInfoFromList(contactListMap.value[key].list, key);
}

function updateCurrentContactInfoFromList(list: IContactInfoType[], type: keyof IContactList) {
  if (
    !(currentContactInfo.value as Friend)?.userID
    && !(currentContactInfo.value as IGroupModel)?.groupID
  ) {
    return;
  }
  if (type === currentContactListKey.value || contactSearchingStatus.value) {
    currentContactInfo.value = list?.find(
      (item: any) =>
        (item?.groupID && item?.groupID === (currentContactInfo.value as IGroupModel)?.groupID) || (item?.userID && item?.userID === (currentContactInfo.value as Friend)?.userID),
    ) || {} as IContactInfoType;
    TUIStore.update(StoreName.CUSTOM, 'currentContactInfo', currentContactInfo.value);
  }
}

function generateRenderKey(contactListMapKey: keyof IContactList, contactInfo: IContactInfoType, index: number) {
  return `${contactListMapKey}-${(contactInfo as Friend).userID || (contactInfo as IGroupModel).groupID || ('index' + index)}`;
}

function onCurrentContactSearchResultUpdated(searchResult: IContactSearchResult) {
  contactSearchResult.value = searchResult;
}

function onCurrentContactSearchingStatusUpdated(searchingStatus: boolean) {
  contactSearchingStatus.value = searchingStatus;
  TUIStore.update(StoreName.CUSTOM, 'currentContactInfo', {} as IContactInfoType);
  TUIStore.update(StoreName.CUSTOM, 'currentContactListKey', '');
}

function onCurrentContactInfoUpdated(contactInfo: IContactInfoType) {
  currentContactInfo.value = contactInfo;
}

function onCurrentContactListKeyUpdated(contactListKey: string) {
  currentContactListKey.value = contactListKey;
}

provide('userOnlineStatusMap', userOnlineStatusMap);
</script>

<style lang="scss" scoped src="./style/index.scss"></style>

<style lang="scss" scoped>
/* 班级选择器样式 */
.tui-class-selector {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 8px;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  &-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  &-school {
    font-size: 12px;
    color: #666;
  }

  &-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  &-option {
    padding: 8px 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 4px;

    &:hover {
      border-color: #007bff;
      background: #f0f8ff;
    }

    &.active {
      background: #007bff;
      border-color: #007bff;
      color: #fff;
    }
  }

  &-count {
    font-size: 12px;
    opacity: 0.8;
  }
}

/* 加载状态样式 */
.tui-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;

  &-text {
    font-size: 14px;
    color: #666;
  }
}

/* H5适配 */
.tui-contact-list-h5 {
  .tui-class-selector {
    padding: 12px;

    &-options {
      gap: 6px;
    }

    &-option {
      padding: 6px 10px;
      font-size: 13px;
    }
  }
}
</style>
