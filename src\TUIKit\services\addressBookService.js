/**
 * 通讯录数据获取服务
 * 根据用户角色调用不同的通讯录接口
 */
import http from '@/utils/http'

/**
 * 通讯录数据服务类
 */
class AddressBookService {
    /**
     * 根据用户角色获取通讯录数据
     * @param {string} roleCode - 用户角色代码 (eltern: 家长, teacher: 老师)
     * @returns {Promise} 返回通讯录数据
     */
    async getAddressBookByRole(roleCode) {
        try {
            let url = ''
            
            // 根据角色选择不同的接口
            if (roleCode === 'eltern') {
                // 家长接口
                url = '/app/addressBookApp/getElternAddressBook'
            } else {
                // 老师接口 (默认)
                url = '/app/addressBookApp/getEmployeeAddressBook'
            }
            
            console.log(`[AddressBookService] 获取通讯录数据，角色: ${roleCode}, 接口: ${url}`)
            
            const response = await http.get(url)
            
            if (response && response.data) {
                console.log(`[AddressBookService] 通讯录数据获取成功:`, response.data)
                return this.formatAddressBookData(response.data)
            } else {
                console.warn(`[AddressBookService] 通讯录数据为空`)
                return this.getEmptyAddressBookData()
            }
        } catch (error) {
            console.error(`[AddressBookService] 获取通讯录数据失败:`, error)
            throw error
        }
    }
    
    /**
     * 格式化通讯录数据
     * @param {Object} rawData - 原始接口数据
     * @returns {Object} 格式化后的数据
     */
    formatAddressBookData(rawData) {
        const { adlist = [], schoolBadgeUrl = '', schoolName = '' } = rawData
        
        return {
            classList: adlist.map(classItem => ({
                id: classItem.id,
                name: classItem.name,
                studentList: this.formatStudentList(classItem.studentList || [])
            })),
            schoolInfo: {
                badgeUrl: schoolBadgeUrl,
                name: schoolName
            }
        }
    }
    
    /**
     * 格式化学生列表数据，适配TUIKit的Friend数据结构
     * @param {Array} studentList - 学生列表
     * @returns {Array} 格式化后的好友列表
     */
    formatStudentList(studentList) {
        return studentList.map((student, index) => ({
            // TUIKit Friend 必需字段
            userID: student.id || `student_${index}`,
            profile: {
                userID: student.id || `student_${index}`,
                avatar: student.avatar || '',
                nick: student.name || '未知学生'
            },
            remark: student.name || '未知学生',
            
            // 扩展字段 - 学生特有信息
            studentInfo: {
                id: student.id,
                name: student.name,
                className: student.className,
                gender: student.gender,
                address: student.address,
                phone: student.phone,
                avatar: student.avatar
            },
            
            // 渲染相关字段
            renderKey: `student_${student.id || index}`,
            addTime: Date.now(), // 模拟添加时间
            
            // 标识这是学生数据而非真实好友
            isStudent: true
        }))
    }
    
    /**
     * 获取空的通讯录数据结构
     * @returns {Object} 空数据结构
     */
    getEmptyAddressBookData() {
        return {
            classList: [],
            schoolInfo: {
                badgeUrl: '',
                name: ''
            }
        }
    }
    
    /**
     * 根据班级ID获取学生列表
     * @param {Array} classList - 班级列表
     * @param {string} classId - 班级ID
     * @returns {Array} 学生列表
     */
    getStudentsByClassId(classList, classId) {
        const targetClass = classList.find(classItem => classItem.id === classId)
        return targetClass ? targetClass.studentList : []
    }
    
    /**
     * 获取默认班级（第一个班级）的学生列表
     * @param {Array} classList - 班级列表
     * @returns {Array} 学生列表
     */
    getDefaultClassStudents(classList) {
        if (classList.length > 0) {
            return classList[0].studentList
        }
        return []
    }
}

// 创建单例实例
const addressBookService = new AddressBookService()

export default addressBookService
