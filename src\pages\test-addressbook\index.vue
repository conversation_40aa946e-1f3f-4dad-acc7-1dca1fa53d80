<template>
  <view class="test-page">
    <view class="test-header">
      <text class="test-title">通讯录功能测试页面</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">用户信息</text>
      <view class="info-item">
        <text>角色: {{ userRole }}</text>
      </view>
      <view class="info-item">
        <text>用户ID: {{ userId }}</text>
      </view>
      <view class="info-item">
        <text>接口字段: {{ userRole === 'eltern' ? 'adlist' : 'classAddBookDTOList' }}</text>
      </view>
      <view class="info-item">
        <text>接口地址: {{ getApiUrl() }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">通讯录数据测试</text>
      <view class="test-buttons">
        <button @click="testGetAddressBook" :disabled="loading">
          {{ loading ? '加载中...' : '获取通讯录数据' }}
        </button>
        <button @click="clearData">清空数据</button>
      </view>
    </view>

    <view v-if="addressBookData.classList.length > 0" class="test-section">
      <text class="section-title">班级列表 ({{ addressBookData.classList.length }}个班级)</text>
      <view class="class-list">
        <view 
          v-for="classItem in addressBookData.classList" 
          :key="classItem.id"
          class="class-item"
          :class="{ active: selectedClassId === classItem.id }"
          @click="selectClass(classItem.id)"
        >
          <text class="class-name">{{ classItem.name }}</text>
          <text class="student-count">({{ classItem.studentList.length }}人)</text>
        </view>
      </view>
    </view>

    <view v-if="currentStudents.length > 0" class="test-section">
      <text class="section-title">学生列表 ({{ currentStudents.length }}人)</text>
      <view class="student-list">
        <view 
          v-for="student in currentStudents" 
          :key="student.userID"
          class="student-item"
        >
          <image 
            :src="student.profile.avatar || '/static/images/default-avatar.png'" 
            class="student-avatar"
            mode="aspectFill"
          />
          <view class="student-info">
            <text class="student-name">{{ student.profile.nick }}</text>
            <text class="student-id">ID: {{ student.userID }}</text>
            <text v-if="student.studentInfo.className" class="student-class">
              班级: {{ student.studentInfo.className }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <view v-if="error" class="error-section">
      <text class="error-title">错误信息:</text>
      <text class="error-message">{{ error }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import useStore from '@/store'
import addressBookService from '@/TUIKit/services/addressBookService.js'

const { user } = useStore()

// 响应式数据
const loading = ref(false)
const error = ref('')
const addressBookData = ref({
  classList: [],
  schoolInfo: { badgeUrl: '', name: '' }
})
const selectedClassId = ref('')

// 计算属性
const userRole = computed(() => user.identityInfo?.roleCode || '未知')
const userId = computed(() => user.userInfo?.id || '未知')

const currentStudents = computed(() => {
  if (!selectedClassId.value || !addressBookData.value.classList.length) {
    return []
  }
  const targetClass = addressBookData.value.classList.find(cls => cls.id === selectedClassId.value)
  return targetClass ? targetClass.studentList : []
})

// 方法
function getApiUrl() {
  return userRole.value === 'eltern'
    ? '/app/addressBookApp/getElternAddressBook'
    : '/app/addressBookApp/getEmployeeAddressBook'
}

async function testGetAddressBook() {
  try {
    loading.value = true
    error.value = ''

    console.log('[测试] 开始获取通讯录数据')
    console.log('[测试] 用户角色:', userRole.value)
    console.log('[测试] 接口地址:', getApiUrl())
    console.log('[测试] 预期字段:', userRole.value === 'eltern' ? 'adlist' : 'classAddBookDTOList')

    const data = await addressBookService.getAddressBookByRole(userRole.value)
    addressBookData.value = data

    // 默认选择第一个班级
    if (data.classList.length > 0) {
      selectedClassId.value = data.classList[0].id
    }

    console.log('[测试] 通讯录数据获取成功:', data)

    uni.showToast({
      title: `获取成功，共${data.classList.length}个班级`,
      icon: 'success'
    })
  } catch (err) {
    console.error('[测试] 获取通讯录数据失败:', err)
    error.value = err.message || '获取数据失败'

    uni.showToast({
      title: '获取数据失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

function selectClass(classId) {
  selectedClassId.value = classId
  console.log('[测试] 选择班级:', classId)
}

function clearData() {
  addressBookData.value = {
    classList: [],
    schoolInfo: { badgeUrl: '', name: '' }
  }
  selectedClassId.value = ''
  error.value = ''
  console.log('[测试] 数据已清空')
}

onMounted(() => {
  console.log('[测试] 页面加载完成')
  console.log('[测试] 当前用户信息:', {
    role: userRole.value,
    userId: userId.value
  })
})
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

.info-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.test-buttons {
  display: flex;
  gap: 12px;
  
  button {
    flex: 1;
    padding: 12px;
    border-radius: 6px;
    border: none;
    background: #007bff;
    color: white;
    font-size: 14px;
    
    &:disabled {
      background: #ccc;
    }
    
    &:last-child {
      background: #6c757d;
    }
  }
}

.class-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.class-item {
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  
  &.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
  }
  
  &:hover {
    border-color: #007bff;
  }
}

.class-name {
  font-weight: bold;
  margin-right: 4px;
}

.student-count {
  font-size: 12px;
  opacity: 0.8;
}

.student-list {
  max-height: 400px;
  overflow-y: auto;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
  background: #f0f0f0;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.student-id,
.student-class {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 2px;
}

.error-section {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 12px;
}

.error-title {
  font-weight: bold;
  color: #721c24;
  display: block;
  margin-bottom: 8px;
}

.error-message {
  color: #721c24;
  font-size: 14px;
}
</style>
